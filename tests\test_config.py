"""Tests for configuration management."""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import patch

import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from util.config import Config


class TestConfig:
    """Test configuration management."""
    
    def test_default_config(self):
        """Test default configuration values."""
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch.dict(os.environ, {'APPDATA': temp_dir}):
                config = Config()
                
                assert config.last_port == ''
                assert config.last_baudrate == 115200
                assert config.theme == 'dark'
                assert config.colorization_enabled is True
                assert config.timestamps_enabled is False
                assert config.auto_scroll is True
                assert config.max_lines == 10000
                assert config.line_ending == 'CRLF'
    
    def test_config_persistence(self):
        """Test configuration save and load."""
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch.dict(os.environ, {'APPDATA': temp_dir}):
                # Create and modify config
                config1 = Config()
                config1.last_port = 'COM3'
                config1.last_baudrate = 921600
                config1.theme = 'light'
                config1.save()
                
                # Create new config instance and verify persistence
                config2 = Config()
                assert config2.last_port == 'COM3'
                assert config2.last_baudrate == 921600
                assert config2.theme == 'light'
    
    def test_config_directory_creation(self):
        """Test that config directory is created."""
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch.dict(os.environ, {'APPDATA': temp_dir}):
                config = Config()
                assert config.config_dir.exists()
                assert config.config_dir.is_dir()
    
    def test_corrupted_config_handling(self):
        """Test handling of corrupted config file."""
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch.dict(os.environ, {'APPDATA': temp_dir}):
                config = Config()
                
                # Write corrupted JSON
                with open(config.config_file, 'w') as f:
                    f.write("invalid json content")
                
                # Should load defaults without crashing
                config.load()
                assert config.last_baudrate == 115200  # Default value
    
    def test_property_setters(self):
        """Test property setters update internal config."""
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch.dict(os.environ, {'APPDATA': temp_dir}):
                config = Config()
                
                config.last_port = 'COM5'
                assert config.get('last_port') == 'COM5'
                
                config.colorization_enabled = False
                assert config.get('colorization_enabled') is False
                
                config.max_lines = 5000
                assert config.get('max_lines') == 5000
