"""Tests for syntax highlighting."""

import pytest
from unittest.mock import Mock

import sys
from pathlib import Path

# Add src directory to path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

# Set up environment for relative imports
import os
os.chdir(str(src_path))

from PySide6.QtWidgets import QApplication
from PySide6.QtGui import QTextDocument

# Import the highlighter class directly
sys.path.append(str(src_path / "gui"))
from terminal_widget import SerialHighlighter


@pytest.fixture
def app():
    """Create QApplication for testing."""
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    return app


@pytest.fixture
def highlighter(app):
    """Create highlighter for testing."""
    document = QTextDocument()
    return SerialHighlighter(document, "dark")


class TestSerialHighlighter:
    """Test syntax highlighting functionality."""
    
    def test_error_classification(self, highlighter):
        """Test error line classification."""
        error_lines = [
            "ERROR: Connection failed",
            "Fatal error occurred",
            "Exception in main thread",
            "FAIL: Test failed",
            "Abort trap: 6",
            "Panic: out of memory"
        ]
        
        for line in error_lines:
            assert highlighter._classify_line(line) == 'error'
    
    def test_warning_classification(self, highlighter):
        """Test warning line classification."""
        warning_lines = [
            "WARNING: Low memory",
            "WARN: Deprecated function",
            "DEBUG: Variable value",
            "INFO: Starting process"
        ]
        
        for line in warning_lines:
            assert highlighter._classify_line(line) == 'warning'
    
    def test_normal_classification(self, highlighter):
        """Test normal line classification."""
        normal_lines = [
            "ESP32 Boot complete",
            "WiFi connected successfully",
            "Temperature: 25.6°C",
            "Ready for commands",
            "AT+GMR"
        ]
        
        for line in normal_lines:
            assert highlighter._classify_line(line) == 'normal'
    
    def test_case_insensitive_matching(self, highlighter):
        """Test case-insensitive pattern matching."""
        assert highlighter._classify_line("error occurred") == 'error'
        assert highlighter._classify_line("ERROR OCCURRED") == 'error'
        assert highlighter._classify_line("Error Occurred") == 'error'
        
        assert highlighter._classify_line("warning message") == 'warning'
        assert highlighter._classify_line("WARNING MESSAGE") == 'warning'
        assert highlighter._classify_line("Warning Message") == 'warning'
    
    def test_theme_switching(self, highlighter):
        """Test theme switching functionality."""
        # Start with dark theme
        assert highlighter.theme == "dark"
        
        # Switch to light theme
        highlighter.set_theme("light")
        assert highlighter.theme == "light"
        
        # Verify formats are updated
        assert 'normal' in highlighter.formats
        assert 'error' in highlighter.formats
        assert 'warning' in highlighter.formats
    
    def test_enable_disable(self, highlighter):
        """Test enabling/disabling highlighting."""
        # Start enabled
        assert highlighter.enabled is True
        
        # Disable
        highlighter.set_enabled(False)
        assert highlighter.enabled is False
        
        # Re-enable
        highlighter.set_enabled(True)
        assert highlighter.enabled is True
    
    def test_timestamp_detection(self, highlighter):
        """Test timestamp detection in lines."""
        # Mock the setFormat method to track calls
        highlighter.setFormat = Mock()
        
        # Test line with timestamp
        line_with_timestamp = "[12:34:56.789] ESP32 Boot complete"
        highlighter.highlightBlock(line_with_timestamp)
        
        # Should have been called for both timestamp and content
        assert highlighter.setFormat.call_count >= 1
    
    def test_highlight_block_disabled(self, highlighter):
        """Test highlighting when disabled."""
        highlighter.set_enabled(False)
        highlighter.setFormat = Mock()
        
        highlighter.highlightBlock("ERROR: Test error")
        
        # Should not call setFormat when disabled
        highlighter.setFormat.assert_not_called()
