"""Main window for ESP Serial Monitor Pro."""

import os
import webbrowser
from typing import List, Optional

from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QIcon, QPixmap, QAction, QFont
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QComboBox, QPushButton, QLabel, QLineEdit, QCheckBox, QSplitter,
    QGroupBox, QListWidget, QTextEdit, QStatusBar, QMenuBar, QMenu,
    QMessageBox, QFileDialog, QProgressBar, QFrame
)

from ..serialio.serial_manager import SerialManager, PortInfo
from ..gui.terminal_widget import TerminalWidget
from ..util.config import config
from ..util.logger import logger


class StatusIndicator(QLabel):
    """LED-style status indicator."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(16, 16)
        self.set_status("disconnected")
    
    def set_status(self, status: str):
        """Set the status indicator color."""
        colors = {
            "connected": "#00ff88",      # Green
            "connecting": "#ffaa00",     # Yellow
            "disconnected": "#ff4444",   # Red
            "error": "#ff4444"           # Red
        }
        
        color = colors.get(status, "#888888")
        self.setStyleSheet(f"""
            QLabel {{
                background-color: {color};
                border-radius: 8px;
                border: 1px solid #333333;
            }}
        """)


class MainWindow(QMainWindow):
    """Main application window."""
    
    def __init__(self):
        super().__init__()
        
        # Initialize components
        self.serial_manager = SerialManager()
        self.current_theme = config.theme
        
        # UI components
        self.terminal_widget = None
        self.port_combo = None
        self.baud_combo = None
        self.connect_button = None
        self.status_indicator = None
        self.send_input = None
        self.line_ending_combo = None
        
        # Setup
        self._setup_ui()
        self._setup_connections()
        self._apply_theme()
        self._restore_settings()
        
        # Start with port refresh
        self._refresh_ports()
    
    def _setup_ui(self):
        """Set up the user interface."""
        self.setWindowTitle("ESP Serial Monitor Pro")
        self.setMinimumSize(1000, 700)
        
        # Set application icon
        icon_path = os.path.join(os.path.dirname(__file__), "..", "..", "icon.ico")
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Create toolbar
        toolbar_layout = self._create_toolbar()
        main_layout.addLayout(toolbar_layout)
        
        # Create main content area
        content_splitter = self._create_content_area()
        main_layout.addWidget(content_splitter)
        
        # Create send area
        send_layout = self._create_send_area()
        main_layout.addLayout(send_layout)
        
        # Create status bar
        self._create_status_bar()
        
        # Create menu bar
        self._create_menu_bar()
    
    def _create_toolbar(self) -> QHBoxLayout:
        """Create the toolbar with connection controls."""
        layout = QHBoxLayout()
        
        # Port selection
        layout.addWidget(QLabel("Port:"))
        self.port_combo = QComboBox()
        self.port_combo.setMinimumWidth(150)
        layout.addWidget(self.port_combo)
        
        # Baud rate selection
        layout.addWidget(QLabel("Baud:"))
        self.baud_combo = QComboBox()
        self.baud_combo.addItems([str(rate) for rate in SerialManager.COMMON_BAUD_RATES])
        self.baud_combo.setCurrentText("115200")
        layout.addWidget(self.baud_combo)
        
        # Connect/Disconnect button
        self.connect_button = QPushButton("Connect")
        self.connect_button.setMinimumWidth(100)
        layout.addWidget(self.connect_button)
        
        # Refresh button
        refresh_button = QPushButton("Refresh")
        refresh_button.clicked.connect(self._refresh_ports)
        layout.addWidget(refresh_button)
        
        # Spacer
        layout.addStretch()
        
        # Open log folder button
        log_folder_button = QPushButton("📁 Open Log Folder")
        log_folder_button.clicked.connect(self._open_log_folder)
        layout.addWidget(log_folder_button)
        
        # Theme toggle button
        theme_button = QPushButton("🌙 Dark" if self.current_theme == "light" else "☀️ Light")
        theme_button.clicked.connect(self._toggle_theme)
        layout.addWidget(theme_button)
        self.theme_button = theme_button
        
        return layout
    
    def _create_content_area(self) -> QSplitter:
        """Create the main content area with terminal and side panels."""
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Recent ports and profiles
        left_panel = self._create_left_panel()
        splitter.addWidget(left_panel)
        
        # Center - Terminal
        self.terminal_widget = TerminalWidget()
        splitter.addWidget(self.terminal_widget)
        
        # Right panel - Device info
        right_panel = self._create_right_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([200, 600, 200])
        
        return splitter
    
    def _create_left_panel(self) -> QWidget:
        """Create the left panel with recent ports and profiles."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Recent ports group
        ports_group = QGroupBox("Recent Ports")
        ports_layout = QVBoxLayout(ports_group)
        self.recent_ports_list = QListWidget()
        self.recent_ports_list.setMaximumHeight(100)
        ports_layout.addWidget(self.recent_ports_list)
        layout.addWidget(ports_group)
        
        # Profiles group
        profiles_group = QGroupBox("Saved Profiles")
        profiles_layout = QVBoxLayout(profiles_group)
        self.profiles_list = QListWidget()
        self.profiles_list.setMaximumHeight(100)
        profiles_layout.addWidget(self.profiles_list)
        layout.addWidget(profiles_group)
        
        # Add some default profiles
        self.profiles_list.addItem("ESP32-Dev (115200)")
        self.profiles_list.addItem("NodeMCU (74880)")
        self.profiles_list.addItem("ESP32-CAM (115200)")
        
        layout.addStretch()
        return panel
    
    def _create_right_panel(self) -> QWidget:
        """Create the right panel with device info."""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Device info group
        info_group = QGroupBox("Device Info")
        info_layout = QVBoxLayout(info_group)
        
        self.device_info = QTextEdit()
        self.device_info.setMaximumHeight(150)
        self.device_info.setReadOnly(True)
        self.device_info.setPlainText("No device connected")
        info_layout.addWidget(self.device_info)
        
        layout.addWidget(info_group)
        
        # Connection status
        status_group = QGroupBox("Status")
        status_layout = QVBoxLayout(status_group)
        
        self.status_indicator = StatusIndicator()
        status_text_layout = QHBoxLayout()
        status_text_layout.addWidget(self.status_indicator)
        status_text_layout.addWidget(QLabel("Disconnected"))
        status_text_layout.addStretch()
        status_layout.addLayout(status_text_layout)
        
        layout.addWidget(status_group)
        
        layout.addStretch()
        return panel
    
    def _create_send_area(self) -> QHBoxLayout:
        """Create the send area with input and controls."""
        layout = QHBoxLayout()
        
        # Send input
        layout.addWidget(QLabel("Send:"))
        self.send_input = QLineEdit()
        self.send_input.setPlaceholderText("Enter command to send...")
        layout.addWidget(self.send_input)
        
        # Line ending selection
        self.line_ending_combo = QComboBox()
        self.line_ending_combo.addItems(["CRLF", "CR", "LF", "None"])
        self.line_ending_combo.setCurrentText(config.line_ending)
        layout.addWidget(self.line_ending_combo)
        
        # Send button
        send_button = QPushButton("Send")
        send_button.clicked.connect(self._send_data)
        layout.addWidget(send_button)
        
        # Send file button
        send_file_button = QPushButton("Send File")
        send_file_button.clicked.connect(self._send_file)
        layout.addWidget(send_file_button)
        
        return layout
    
    def _create_status_bar(self):
        """Create the status bar."""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)
        
        # Connection status
        self.status_label = QLabel("Disconnected")
        status_bar.addWidget(self.status_label)
        
        # Line count
        self.line_count_label = QLabel("0 lines")
        status_bar.addWidget(self.line_count_label)
        
        # Footer with clickable link
        footer_label = QLabel('by Sk Raihan | <a href="https://www.skrelectronicslab.com" style="color: #00d4ff;">SKR Electronics Lab</a>')
        footer_label.setOpenExternalLinks(True)
        footer_label.linkActivated.connect(self._open_website)
        status_bar.addPermanentWidget(footer_label)
    
    def _create_menu_bar(self):
        """Create the menu bar."""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("File")
        
        save_log_action = QAction("Save Log...", self)
        save_log_action.setShortcut("Ctrl+S")
        save_log_action.triggered.connect(self.terminal_widget.save_log)
        file_menu.addAction(save_log_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("Exit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # View menu
        view_menu = menubar.addMenu("View")
        
        clear_action = QAction("Clear Terminal", self)
        clear_action.setShortcut("Ctrl+L")
        clear_action.triggered.connect(self.terminal_widget.clear_terminal)
        view_menu.addAction(clear_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("Tools")
        
        settings_action = QAction("Settings...", self)
        settings_action.triggered.connect(self._open_settings)
        tools_menu.addAction(settings_action)

    def _setup_connections(self):
        """Set up signal connections."""
        # Serial manager connections
        self.serial_manager.connected.connect(self._on_connected)
        self.serial_manager.disconnected.connect(self._on_disconnected)
        self.serial_manager.connection_failed.connect(self._on_connection_failed)
        self.serial_manager.line_received.connect(self.terminal_widget.append_line)
        self.serial_manager.ports_changed.connect(self._on_ports_changed)

        # UI connections
        self.connect_button.clicked.connect(self._toggle_connection)
        self.send_input.returnPressed.connect(self._send_data)

        # Port selection
        self.port_combo.currentTextChanged.connect(self._on_port_selected)
        self.baud_combo.currentTextChanged.connect(self._on_baud_selected)

        # Line ending selection
        self.line_ending_combo.currentTextChanged.connect(self._on_line_ending_changed)

    def _apply_theme(self):
        """Apply the current theme to the UI."""
        if self.current_theme == "dark":
            self._apply_dark_theme()
        else:
            self._apply_light_theme()

        # Update terminal theme
        if self.terminal_widget:
            self.terminal_widget.set_theme(self.current_theme)

    def _apply_dark_theme(self):
        """Apply dark theme styling."""
        dark_style = """
        QMainWindow {
            background-color: #1a1a2e;
            color: #e0e0e0;
        }
        QWidget {
            background-color: #1a1a2e;
            color: #e0e0e0;
        }
        QComboBox, QLineEdit, QPushButton {
            background-color: #16213e;
            border: 1px solid #0f3460;
            padding: 5px;
            border-radius: 3px;
            color: #e0e0e0;
        }
        QComboBox:hover, QPushButton:hover {
            background-color: #0f3460;
        }
        QComboBox:pressed, QPushButton:pressed {
            background-color: #00d4ff;
            color: #1a1a2e;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #0f3460;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QListWidget, QTextEdit, QPlainTextEdit {
            background-color: #16213e;
            border: 1px solid #0f3460;
            border-radius: 3px;
        }
        QStatusBar {
            background-color: #16213e;
            border-top: 1px solid #0f3460;
        }
        QMenuBar {
            background-color: #16213e;
            border-bottom: 1px solid #0f3460;
        }
        QMenuBar::item {
            background-color: transparent;
            padding: 5px 10px;
        }
        QMenuBar::item:selected {
            background-color: #0f3460;
        }
        QMenu {
            background-color: #16213e;
            border: 1px solid #0f3460;
        }
        QMenu::item:selected {
            background-color: #0f3460;
        }
        """
        self.setStyleSheet(dark_style)

    def _apply_light_theme(self):
        """Apply light theme styling."""
        light_style = """
        QMainWindow {
            background-color: #ffffff;
            color: #333333;
        }
        QWidget {
            background-color: #ffffff;
            color: #333333;
        }
        QComboBox, QLineEdit, QPushButton {
            background-color: #f5f5f5;
            border: 1px solid #cccccc;
            padding: 5px;
            border-radius: 3px;
            color: #333333;
        }
        QComboBox:hover, QPushButton:hover {
            background-color: #e0e0e0;
        }
        QComboBox:pressed, QPushButton:pressed {
            background-color: #007acc;
            color: #ffffff;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QListWidget, QTextEdit, QPlainTextEdit {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            border-radius: 3px;
        }
        QStatusBar {
            background-color: #f5f5f5;
            border-top: 1px solid #cccccc;
        }
        QMenuBar {
            background-color: #f5f5f5;
            border-bottom: 1px solid #cccccc;
        }
        QMenuBar::item {
            background-color: transparent;
            padding: 5px 10px;
        }
        QMenuBar::item:selected {
            background-color: #e0e0e0;
        }
        QMenu {
            background-color: #ffffff;
            border: 1px solid #cccccc;
        }
        QMenu::item:selected {
            background-color: #e0e0e0;
        }
        """
        self.setStyleSheet(light_style)

    def _restore_settings(self):
        """Restore settings from configuration."""
        # Restore last port and baud rate
        if config.last_port:
            index = self.port_combo.findText(config.last_port)
            if index >= 0:
                self.port_combo.setCurrentIndex(index)

        self.baud_combo.setCurrentText(str(config.last_baudrate))
        self.line_ending_combo.setCurrentText(config.line_ending)

        # Restore window geometry if available
        geometry = config.get('window_geometry')
        if geometry:
            self.restoreGeometry(geometry)

    def _save_settings(self):
        """Save current settings to configuration."""
        config.last_port = self.port_combo.currentText()
        config.last_baudrate = int(self.baud_combo.currentText())
        config.line_ending = self.line_ending_combo.currentText()
        config.theme = self.current_theme
        config.set('window_geometry', self.saveGeometry())
        config.save()

    def _refresh_ports(self):
        """Refresh the list of available ports."""
        current_port = self.port_combo.currentText()
        self.port_combo.clear()

        ports = self.serial_manager.get_available_ports()
        for port in ports:
            self.port_combo.addItem(port.friendly_name, port.device)

        # Restore selection if possible
        if current_port:
            index = self.port_combo.findText(current_port)
            if index >= 0:
                self.port_combo.setCurrentIndex(index)

        logger.info(f"Found {len(ports)} serial ports")

    def _toggle_connection(self):
        """Toggle serial connection."""
        if self.serial_manager.is_connected():
            self._disconnect()
        else:
            self._connect()

    def _connect(self):
        """Connect to the selected serial port."""
        port_text = self.port_combo.currentText()
        if not port_text:
            QMessageBox.warning(self, "Warning", "Please select a port first.")
            return

        # Extract port device from combo box data
        port_device = self.port_combo.currentData()
        if not port_device:
            # Fallback to parsing the text
            port_device = port_text.split(" - ")[0] if " - " in port_text else port_text

        baudrate = int(self.baud_combo.currentText())

        self.status_indicator.set_status("connecting")
        self.status_label.setText("Connecting...")
        self.connect_button.setText("Connecting...")
        self.connect_button.setEnabled(False)

        # Attempt connection
        success = self.serial_manager.connect(port_device, baudrate)

        if not success:
            self.status_indicator.set_status("error")
            self.status_label.setText("Connection failed")
            self.connect_button.setText("Connect")
            self.connect_button.setEnabled(True)

    def _disconnect(self):
        """Disconnect from the serial port."""
        self.serial_manager.disconnect()

    def _send_data(self):
        """Send data from the input field."""
        data = self.send_input.text()
        if not data:
            return

        line_ending = self.line_ending_combo.currentText()
        success = self.serial_manager.send_data(data, line_ending)

        if success:
            self.send_input.clear()
            # Echo sent data to terminal
            echo_text = f">> {data}"
            self.terminal_widget.append_line(echo_text)
        else:
            QMessageBox.warning(self, "Warning", "Failed to send data. Check connection.")

    def _send_file(self):
        """Send a file line by line."""
        filename, _ = QFileDialog.getOpenFileName(
            self,
            "Select File to Send",
            "",
            "Text Files (*.txt);;All Files (*)"
        )

        if filename:
            # TODO: Implement file sending with progress dialog
            QMessageBox.information(self, "Info", "File sending feature coming soon!")

    def _toggle_theme(self):
        """Toggle between light and dark themes."""
        self.current_theme = "light" if self.current_theme == "dark" else "dark"
        self._apply_theme()

        # Update button text
        self.theme_button.setText("🌙 Dark" if self.current_theme == "light" else "☀️ Light")

    def _open_log_folder(self):
        """Open the log folder in file explorer."""
        log_dir = config.config_dir / "logs"
        log_dir.mkdir(exist_ok=True)

        import subprocess
        subprocess.run(["explorer", str(log_dir)], shell=True)

    def _open_settings(self):
        """Open settings dialog."""
        # TODO: Implement settings dialog
        QMessageBox.information(self, "Info", "Settings dialog coming soon!")

    def _open_website(self, url: str):
        """Open website in default browser."""
        webbrowser.open(url)

    # Event handlers
    def _on_connected(self, port: str):
        """Handle successful connection."""
        self.status_indicator.set_status("connected")
        self.status_label.setText(f"Connected to {port}")
        self.connect_button.setText("Disconnect")
        self.connect_button.setEnabled(True)

        # Update device info
        self.device_info.setPlainText(f"Connected to: {port}\nBaud rate: {self.baud_combo.currentText()}")

        # Add to recent ports
        self._add_to_recent_ports(port)

        logger.info(f"Connected to {port}")

    def _on_disconnected(self):
        """Handle disconnection."""
        self.status_indicator.set_status("disconnected")
        self.status_label.setText("Disconnected")
        self.connect_button.setText("Connect")
        self.connect_button.setEnabled(True)

        # Clear device info
        self.device_info.setPlainText("No device connected")

        logger.info("Disconnected from serial port")

    def _on_connection_failed(self, error: str):
        """Handle connection failure."""
        self.status_indicator.set_status("error")
        self.status_label.setText("Connection failed")
        self.connect_button.setText("Connect")
        self.connect_button.setEnabled(True)

        QMessageBox.critical(self, "Connection Error", f"Failed to connect:\n{error}")
        logger.error(f"Connection failed: {error}")

    def _on_ports_changed(self, ports: List[PortInfo]):
        """Handle port list changes."""
        current_port = self.port_combo.currentText()
        self.port_combo.clear()

        for port in ports:
            self.port_combo.addItem(port.friendly_name, port.device)

        # Try to restore selection
        if current_port:
            index = self.port_combo.findText(current_port)
            if index >= 0:
                self.port_combo.setCurrentIndex(index)

    def _on_port_selected(self, port_text: str):
        """Handle port selection change."""
        if port_text:
            config.last_port = port_text

    def _on_baud_selected(self, baud_text: str):
        """Handle baud rate selection change."""
        if baud_text:
            config.last_baudrate = int(baud_text)

    def _on_line_ending_changed(self, line_ending: str):
        """Handle line ending selection change."""
        config.line_ending = line_ending

    def _add_to_recent_ports(self, port: str):
        """Add port to recent ports list."""
        # Check if already in list
        for i in range(self.recent_ports_list.count()):
            if self.recent_ports_list.item(i).text() == port:
                return

        # Add to top of list
        self.recent_ports_list.insertItem(0, port)

        # Limit to 5 recent ports
        while self.recent_ports_list.count() > 5:
            self.recent_ports_list.takeItem(self.recent_ports_list.count() - 1)

    def closeEvent(self, event):
        """Handle application close event."""
        # Save settings
        self._save_settings()

        # Clean up serial manager
        self.serial_manager.cleanup()

        # Accept the close event
        event.accept()
        logger.info("Application closed")
