"""Main application entry point for ESP Serial Monitor Pro."""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_dir = Path(__file__).parent
sys.path.insert(0, str(src_dir))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from gui.main_window import MainWindow
from util.logger import logger
from util.config import config


def main():
    """Main application entry point."""
    # Create QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("ESP Serial Monitor Pro")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("SKR Electronics Lab")
    app.setOrganizationDomain("skrelectronicslab.com")
    
    # Set application icon
    icon_path = Path(__file__).parent.parent / "icon.ico"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    # Enable high DPI scaling
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    logger.info("Starting ESP Serial Monitor Pro")
    
    try:
        # Create and show main window
        window = MainWindow()
        window.show()
        
        # Run application
        exit_code = app.exec()
        
        logger.info(f"Application exited with code {exit_code}")
        return exit_code
        
    except Exception as e:
        logger.error(f"Unhandled exception: {e}", exc_info=True)
        return 1


if __name__ == "__main__":
    sys.exit(main())
