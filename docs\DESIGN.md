# ESP Serial Monitor Pro - Design Document

## Application Overview
ESP Serial Monitor Pro is a professional-grade serial monitor application designed specifically for ESP32/ESP8266 development. It provides robust serial communication, intelligent colorization, and a modern dark-blue themed interface.

## UI Wireframe

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ ESP Serial Monitor Pro                                          [_] [□] [×] │
├─────────────────────────────────────────────────────────────────────────────┤
│ [Port: COM3 ▼] [Baud: 115200 ▼] [Connect] [Refresh] [📁 Open Log Folder]   │
├─────────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────────────────────────────┐ ┌─────────────────┐ │
│ │ Recent      │ │ Terminal Output                     │ │ Device Info     │ │
│ │ Ports       │ │                                     │ │                 │ │
│ │             │ │ [12:34:56.789] ESP32 Boot...       │ │ Chip: ESP32     │ │
│ │ • COM3      │ │ [12:34:57.123] WiFi connecting...   │ │ MAC: AA:BB:CC   │ │
│ │ • COM5      │ │ [12:34:57.456] ERROR: Failed!       │ │ Flash: 4MB      │ │
│ │             │ │ [12:34:57.789] WARNING: Low memory  │ │                 │ │
│ │ Profiles    │ │ [12:34:58.012] Ready for commands   │ │ Status: ●       │ │
│ │ • ESP32-Dev │ │                                     │ │ Connected       │ │
│ │ • NodeMCU   │ │                                     │ │                 │ │
│ └─────────────┘ └─────────────────────────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────┤
│ Send: [AT+GMR                                    ] [CR▼] [Send] [Send File] │
│ RTS: [☐] DTR: [☐]  Auto-scroll: [☑]  Timestamps: [☑]  Find: [_______] [🔍] │
├─────────────────────────────────────────────────────────────────────────────┤
│ ● Connected | 1,234 lines | by Sk Raihan | SKR Electronics Lab             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Color Scheme

### Dark Theme (Default)
- **Background**: Deep Navy (#1a1a2e)
- **Primary**: Electric Blue (#0f3460)
- **Secondary**: Steel Blue (#16213e)
- **Accent**: Bright Blue (#00d4ff)
- **Text Primary**: Light Gray (#e0e0e0)
- **Text Secondary**: Medium Gray (#b0b0b0)
- **Success**: Green (#00ff88)
- **Warning**: Yellow (#ffaa00)
- **Error**: Red (#ff4444)

### Light Theme
- **Background**: White (#ffffff)
- **Primary**: Light Blue (#f0f8ff)
- **Secondary**: Light Gray (#f5f5f5)
- **Accent**: Blue (#007acc)
- **Text Primary**: Dark Gray (#333333)
- **Text Secondary**: Medium Gray (#666666)
- **Success**: Green (#008844)
- **Warning**: Orange (#cc6600)
- **Error**: Red (#cc0000)

## Thread Model

### Main Thread (GUI Thread)
- **Responsibilities**: All GUI operations, event handling, user interactions
- **Communication**: Receives signals from worker threads
- **Never blocks**: No direct serial I/O or long-running operations

### SerialReaderThread (QThread)
```python
class SerialReaderThread(QThread):
    line_received = Signal(str)
    error_occurred = Signal(str)
    connection_lost = Signal()
    
    def run(self):
        while self.running:
            try:
                if self.serial.in_waiting:
                    data = self.serial.read(self.serial.in_waiting)
                    # Process and emit lines
            except SerialException:
                self.error_occurred.emit("Connection lost")
                break
```

### PortWatcherThread (QThread)
```python
class PortWatcherThread(QThread):
    ports_changed = Signal(list)
    
    def run(self):
        while self.running:
            current_ports = list_ports.comports()
            if current_ports != self.last_ports:
                self.ports_changed.emit(current_ports)
            time.sleep(1.0)  # 1 second polling
```

### SendWorkerThread (QThread)
- **Purpose**: Handle file sending with configurable delays
- **Features**: Line-by-line transmission, progress reporting, cancellation support

## Colorization Rules

### Rule Engine
```python
class LineClassifier:
    def __init__(self):
        self.rules = [
            (r'(?i)(error|fail|exception|err|fatal)', 'error'),
            (r'(?i)(warn|warning|debug)', 'warning'),
            (r'.*', 'normal')  # Default rule
        ]
    
    def classify_line(self, line: str) -> str:
        for pattern, category in self.rules:
            if re.match(pattern, line):
                return category
        return 'normal'
```

### Implementation Strategy
- **Primary**: QSyntaxHighlighter for real-time highlighting
- **Fallback**: HTML formatting for complex scenarios
- **Performance**: Batch processing for high-throughput scenarios

## Architecture Components

### Core Modules

#### 1. SerialManager (`serialio/serial_manager.py`)
```python
class SerialManager:
    def __init__(self):
        self.serial_port = None
        self.reader_thread = None
        self.port_watcher = None
    
    def connect(self, port: str, baudrate: int) -> bool
    def disconnect(self) -> None
    def send_data(self, data: str, line_ending: str) -> bool
    def get_available_ports(self) -> List[PortInfo]
    def set_rts_dtr(self, rts: bool, dtr: bool) -> None
```

#### 2. TerminalWidget (`gui/terminal_widget.py`)
```python
class TerminalWidget(QPlainTextEdit):
    def __init__(self):
        super().__init__()
        self.highlighter = SerialHighlighter(self.document())
        self.line_buffer = []
        self.max_lines = 10000
    
    def append_line(self, line: str, timestamp: bool = False)
    def clear_terminal(self)
    def save_log(self, filename: str)
    def find_text(self, text: str, case_sensitive: bool = False)
```

#### 3. MainWindow (`gui/main_window.py`)
```python
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.serial_manager = SerialManager()
        self.terminal_widget = TerminalWidget()
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self)
    def connect_serial(self)
    def disconnect_serial(self)
    def toggle_theme(self)
    def open_settings(self)
```

### Configuration Management

#### Config Structure (`util/config.py`)
```python
class Config:
    def __init__(self):
        self.config_dir = os.path.join(os.getenv('APPDATA'), 'ESPSerialMonitorPro')
        self.config_file = os.path.join(self.config_dir, 'config.json')
        
    default_config = {
        'last_port': '',
        'last_baudrate': 115200,
        'theme': 'dark',
        'colorization_enabled': True,
        'timestamps_enabled': False,
        'auto_scroll': True,
        'max_lines': 10000,
        'window_geometry': None,
        'line_ending': 'CRLF'
    }
```

## Error Handling Strategy

### Serial Communication Errors
1. **Connection Failures**: Display user-friendly messages with suggestions
2. **Permission Errors**: Guide user to close other applications or run as admin
3. **Device Disconnection**: Automatic reconnection attempts with visual feedback
4. **Data Corruption**: Graceful handling with error replacement characters

### GUI Error Handling
1. **Non-blocking Operations**: All serial I/O in background threads
2. **Progress Feedback**: Visual indicators for long-running operations
3. **Graceful Degradation**: Application remains functional during errors
4. **User Notifications**: Toast-style notifications for important events

## Performance Optimizations

### Text Display
- **Buffer Management**: Circular buffer with configurable size limit
- **Batch Updates**: Coalesce multiple lines into single GUI updates
- **Lazy Rendering**: Only render visible text portions
- **Memory Management**: Automatic cleanup of old log entries

### Threading Efficiency
- **Signal Coalescing**: Batch multiple signals to reduce GUI updates
- **Adaptive Polling**: Adjust port monitoring frequency based on activity
- **Resource Cleanup**: Proper thread termination and resource disposal

## Testing Strategy

### Unit Tests
- **Serial Manager**: Mock pySerial for isolated testing
- **Configuration**: Test persistence and migration
- **Colorization**: Verify rule engine accuracy
- **Threading**: Test signal/slot communication

### Integration Tests
- **Mock Hardware**: Simulate ESP32/ESP8266 communication patterns
- **GUI Testing**: Automated UI interaction testing
- **Performance**: Stress testing with high-throughput data

### Hardware Testing
- **Real Devices**: Test with actual ESP32/ESP8266 boards
- **Edge Cases**: Test disconnection, reconnection, error scenarios
- **Cross-platform**: Verify behavior on different Windows versions

## Packaging Configuration

### PyInstaller Specification
```python
# pyinstaller.spec
a = Analysis(['src/app.py'],
             pathex=[],
             binaries=[],
             datas=[('assets/', 'assets/')],
             hiddenimports=['PySide6.QtCore', 'PySide6.QtWidgets', 'PySide6.QtGui'],
             hookspath=[],
             runtime_hooks=[],
             excludes=[],
             win_no_prefer_redirects=False,
             win_private_assemblies=False,
             cipher=block_cipher,
             noarchive=False)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(pyz,
          a.scripts,
          a.binaries,
          a.zipfiles,
          a.datas,
          [],
          name='ESP_Serial_Monitor_Pro',
          debug=False,
          bootloader_ignore_signals=False,
          strip=False,
          upx=True,
          upx_exclude=[],
          runtime_tmpdir=None,
          console=False,
          icon='assets/icon.ico')
```

## Security Considerations
- **No Telemetry**: Completely offline application
- **Local Storage**: All data stored locally in user's AppData
- **Permission Model**: Request minimal required permissions
- **Code Signing**: Consider code signing for distribution trust
