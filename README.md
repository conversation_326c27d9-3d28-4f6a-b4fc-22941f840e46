# ESP Serial Monitor Pro

A professional-grade serial monitor application designed specifically for ESP32/ESP8266 development. Built with Python and PySide6, featuring a modern dark-blue themed interface, intelligent colorization, and robust serial communication.

![ESP Serial Monitor Pro](docs/screenshot_dark.png)

## Features

### Core Functionality
- **Auto Port Detection**: Automatically detects and monitors serial port changes
- **Robust Serial Communication**: Built on pySerial with comprehensive error handling
- **Real-time Monitoring**: Non-blocking serial reading with dedicated threads
- **Auto-reconnect**: Automatic reconnection with exponential backoff when connection is lost
- **Send Commands**: Send text commands with configurable line endings (CR, LF, CRLF, None)
- **File Sending**: Send text files line-by-line with configurable delays
- **RTS/DTR Control**: Manual control of RTS and DTR lines for ESP boot mode control

### User Interface
- **Modern Theming**: Dark-blue theme by default with light theme option
- **Intelligent Colorization**: 
  - Red for errors (error, fail, exception, fatal, etc.)
  - Yellow for warnings (warn, warning, debug, info)
  - Green for normal output
- **Timestamps**: Optional timestamp prefixing with millisecond precision
- **Auto-scroll**: Smart auto-scrolling that pauses when user scrolls up
- **Search**: Find text within terminal output
- **Log Management**: Save logs to file, configurable line limits

### Device Support
- **ESP32**: All variants (WROOM, WROVER, S2, S3, C3, etc.)
- **ESP8266**: NodeMCU, Wemos D1, bare modules
- **Common Baud Rates**: 9600, 74880, 115200, 230400, 460800, 921600
- **Hardware Control**: RTS/DTR toggling for boot mode control

## Installation

### Option 1: Download Pre-built Executable (Recommended)
1. Download `ESP_Serial_Monitor_Pro.exe` from the releases page
2. Run the executable directly - no installation required
3. The application is portable and stores settings in `%APPDATA%\ESPSerialMonitorPro`

### Option 2: Run from Source
1. **Prerequisites**:
   - Python 3.11 or higher
   - Git (optional)

2. **Clone or download the repository**:
   ```bash
   git clone https://github.com/your-repo/esp-serial-monitor-pro.git
   cd esp-serial-monitor-pro
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the application**:
   ```bash
   python src/app.py
   ```

## Building from Source

### Prerequisites
- Python 3.11+
- Windows (for .exe building)

### Build Steps
1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-repo/esp-serial-monitor-pro.git
   cd esp-serial-monitor-pro
   ```

2. **Run the build script**:
   ```bash
   build.bat
   ```

The build script will:
- Create a virtual environment
- Install dependencies
- Run tests
- Build the executable with PyInstaller
- Perform a smoke test

The final executable will be in the `dist/` folder.

### Manual Build
If you prefer to build manually:

```bash
# Create virtual environment
python -m venv venv
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run tests
python -m pytest tests/ -v

# Build executable
pyinstaller pyinstaller.spec
```

## Usage

### Getting Started
1. **Launch the application**
2. **Select a port** from the dropdown (auto-detected)
3. **Choose baud rate** (115200 is common for ESP32/ESP8266)
4. **Click Connect** to establish connection
5. **Monitor output** in the terminal area
6. **Send commands** using the input field at the bottom

### Key Features

#### Port Management
- Ports are automatically detected and the list updates when devices are plugged/unplugged
- Recent ports are remembered and shown in the left panel
- Saved profiles for common device configurations

#### Terminal Output
- **Colorization**: Errors in red, warnings in yellow, normal output in green
- **Timestamps**: Toggle timestamps with `[HH:MM:SS.mmm]` format
- **Auto-scroll**: Automatically scrolls to new output, pauses when you scroll up
- **Search**: Use Ctrl+F to find text in the output
- **Save Logs**: Save terminal output to text files

#### Sending Data
- **Text Input**: Type commands in the send box and press Enter or click Send
- **Line Endings**: Choose CR, LF, CRLF, or None
- **File Sending**: Send text files line-by-line (useful for AT command scripts)
- **RTS/DTR Control**: Toggle RTS and DTR lines for ESP boot mode control

#### Themes
- **Dark Theme**: Default dark-blue professional theme
- **Light Theme**: Clean light theme for different preferences
- **Toggle**: Switch themes instantly with the theme button

### Keyboard Shortcuts
- `Ctrl+S`: Save log to file
- `Ctrl+L`: Clear terminal
- `Ctrl+Q`: Quit application
- `Enter`: Send command (when in send input field)

## Configuration

Settings are automatically saved to `%APPDATA%\ESPSerialMonitorPro\config.json`:

```json
{
  "last_port": "COM3",
  "last_baudrate": 115200,
  "theme": "dark",
  "colorization_enabled": true,
  "timestamps_enabled": false,
  "auto_scroll": true,
  "max_lines": 10000,
  "line_ending": "CRLF",
  "auto_reconnect": true
}
```

## Troubleshooting

### Common Issues

**"Access Denied" or "Port in use"**
- Close other applications using the serial port (Arduino IDE, PuTTY, etc.)
- Try running as Administrator
- Unplug and reconnect the device

**Port not detected**
- Check device drivers are installed
- Try a different USB cable
- Check Windows Device Manager for COM ports

**Connection keeps dropping**
- Check USB cable quality
- Verify power supply to the device
- Try a different USB port
- Disable auto-reconnect if causing issues

**Application won't start**
- Ensure you have the Visual C++ Redistributable installed
- Try running from command line to see error messages
- Check Windows Event Viewer for application errors

### Debug Mode
Run from command line to see debug output:
```bash
ESP_Serial_Monitor_Pro.exe
```

## Development

### Project Structure
```
esp-serial-monitor-pro/
├── src/
│   ├── app.py              # Main application entry point
│   ├── gui/
│   │   ├── main_window.py  # Main window implementation
│   │   └── terminal_widget.py # Terminal with syntax highlighting
│   ├── serialio/
│   │   └── serial_manager.py # Serial port management
│   └── util/
│       ├── config.py       # Configuration management
│       └── logger.py       # Logging utilities
├── tests/                  # Unit tests
├── docs/                   # Documentation
├── icon.ico               # Application icon
├── requirements.txt       # Python dependencies
├── pyinstaller.spec      # PyInstaller build configuration
└── build.bat             # Windows build script
```

### Running Tests
```bash
python -m pytest tests/ -v
```

### Code Style
The project uses:
- **Black** for code formatting
- **Ruff** for linting
- **Type hints** for better code documentation

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Credits

**ESP Serial Monitor Pro** is developed by **Sk Raihan** from **[SKR Electronics Lab](https://www.skrelectronicslab.com)**.

### Acknowledgments
- Built with [PySide6](https://doc.qt.io/qtforpython/) (Qt for Python)
- Serial communication via [pySerial](https://pyserial.readthedocs.io/)
- Inspired by the Arduino IDE Serial Monitor
- Research based on [esptool](https://github.com/espressif/esptool), [Tasmotizer](https://github.com/tasmota/tasmotizer), and other ESP development tools

## Support

For support, bug reports, or feature requests:
- Visit [SKR Electronics Lab](https://www.skrelectronicslab.com)
- Open an issue on GitHub
- Contact: [<EMAIL>](mailto:<EMAIL>)

---

**ESP Serial Monitor Pro** - Professional ESP32/ESP8266 Development Tool  
*by Sk Raihan | SKR Electronics Lab*
