# ESP Serial Monitor Pro - Hardware Testing Checklist

## Overview
This document outlines the hardware testing procedures for ESP Serial Monitor Pro to ensure compatibility with real ESP32/ESP8266 devices.

## Test Environment
- **Application**: ESP Serial Monitor Pro v1.0.0
- **OS**: Windows 10/11
- **Test Date**: 2025-08-21
- **Tester**: Development Team

## Required Hardware

### ESP32 Boards
- [ ] ESP32 WROOM DevKit
- [ ] ESP32-S2 DevKit
- [ ] ESP32-S3 DevKit
- [ ] ESP32-C3 DevKit
- [ ] ESP32-CAM Module

### ESP8266 Boards
- [ ] NodeMCU v1.0 (ESP8266)
- [ ] Wemos D1 Mini
- [ ] ESP8266-01 Module
- [ ] ESP8266-12E DevKit

### USB Cables & Adapters
- [ ] USB-A to Micro-USB cables
- [ ] USB-A to USB-C cables
- [ ] USB-to-TTL adapters (for bare modules)

## Test Procedures

### Test 1: Port Detection
**Objective**: Verify automatic port detection and monitoring

**Steps**:
1. Launch ESP Serial Monitor Pro
2. Note initial port list
3. Connect ESP32/ESP8266 device
4. Verify port appears in dropdown within 2 seconds
5. Disconnect device
6. Verify port disappears from dropdown within 2 seconds
7. Reconnect device multiple times

**Expected Results**:
- [ ] Ports appear automatically when devices connected
- [ ] Ports disappear when devices disconnected
- [ ] Port descriptions show device information
- [ ] No application crashes during plug/unplug

**Test Results**:
```
Device: ________________
Port Detected: _________
Description: ___________
VID/PID: ______________
Status: _______________
```

### Test 2: Connection Establishment
**Objective**: Test connection to various ESP devices

**Steps**:
1. Connect ESP32 device
2. Select port from dropdown
3. Set baud rate to 115200
4. Click "Connect"
5. Verify connection status indicator turns green
6. Repeat for different baud rates: 74880, 460800, 921600

**Expected Results**:
- [ ] Connection establishes successfully
- [ ] Status indicator shows "Connected"
- [ ] No error messages displayed
- [ ] Connection stable for 30+ seconds

**Test Results**:
```
Device: ________________
Baud Rate: _____________
Connection Time: _______
Status: _______________
Notes: ________________
```

### Test 3: Boot Message Reception
**Objective**: Verify reception of ESP boot messages

**Steps**:
1. Connect to ESP device at 74880 baud (ESP8266) or 115200 (ESP32)
2. Press reset button on device
3. Observe boot messages in terminal
4. Verify timestamps (if enabled)
5. Check colorization of messages

**Expected Results**:
- [ ] Boot messages appear in terminal
- [ ] Text is readable and properly decoded
- [ ] Timestamps accurate (if enabled)
- [ ] No garbled characters
- [ ] Colorization applied correctly

**Sample Boot Messages**:
```
ESP32: rst:0x1 (POWERON_RESET),boot:0x13 (SPI_FAST_FLASH_BOOT)
ESP8266: ets Jan  8 2013,rst cause:2, boot mode:(3,6)
```

### Test 4: Command Sending
**Objective**: Test sending commands to ESP devices

**Steps**:
1. Connect to ESP device running AT firmware or custom firmware
2. Send "AT" command with CRLF line ending
3. Verify response received
4. Send "AT+GMR" command
5. Test different line endings (CR, LF, CRLF, None)
6. Send invalid commands and verify error handling

**Expected Results**:
- [ ] Commands sent successfully
- [ ] Responses received and displayed
- [ ] Line endings work correctly
- [ ] Echo of sent commands visible
- [ ] Error responses handled gracefully

**Test Commands**:
```
Command: AT
Response: _______________

Command: AT+GMR
Response: _______________

Command: Invalid
Response: _______________
```

### Test 5: High-Throughput Data
**Objective**: Test performance with high-volume serial data

**Steps**:
1. Connect to ESP device
2. Program device to send continuous data (e.g., sensor readings every 100ms)
3. Monitor for 5 minutes
4. Check for:
   - UI responsiveness
   - Memory usage
   - Data loss
   - Application stability

**Expected Results**:
- [ ] UI remains responsive
- [ ] No data loss observed
- [ ] Memory usage stable
- [ ] No application freezing
- [ ] Auto-scroll works correctly

**Performance Metrics**:
```
Data Rate: _____________
Duration: ______________
UI Responsive: _________
Memory Usage: __________
Data Loss: ____________
```

### Test 6: Connection Recovery
**Objective**: Test auto-reconnect functionality

**Steps**:
1. Establish connection to ESP device
2. Physically disconnect USB cable
3. Verify disconnection detected
4. Reconnect USB cable
5. Observe auto-reconnect attempts
6. Verify successful reconnection

**Expected Results**:
- [ ] Disconnection detected within 5 seconds
- [ ] Auto-reconnect attempts begin
- [ ] Successful reconnection when device available
- [ ] Status indicators update correctly
- [ ] No manual intervention required

**Reconnection Test**:
```
Disconnection Detected: ________
Reconnect Attempts: ___________
Reconnection Time: ____________
Success Rate: ________________
```

### Test 7: RTS/DTR Control
**Objective**: Test hardware flow control lines

**Steps**:
1. Connect ESP device
2. Toggle RTS checkbox
3. Toggle DTR checkbox
4. Use RTS/DTR to reset ESP into boot mode
5. Verify device responds to line changes

**Expected Results**:
- [ ] RTS line toggles correctly
- [ ] DTR line toggles correctly
- [ ] ESP device responds to line changes
- [ ] Boot mode entry possible via RTS/DTR
- [ ] No errors when toggling lines

**RTS/DTR Test**:
```
RTS Toggle: ___________
DTR Toggle: ___________
Boot Mode Entry: ______
Device Response: ______
```

### Test 8: Multiple Device Support
**Objective**: Test handling multiple connected devices

**Steps**:
1. Connect multiple ESP devices simultaneously
2. Verify all ports detected
3. Connect to one device
4. Disconnect and connect to another
5. Test rapid switching between devices

**Expected Results**:
- [ ] All devices detected simultaneously
- [ ] Port list updates correctly
- [ ] Switching between devices works
- [ ] No port conflicts
- [ ] Stable connections to each device

**Multi-Device Test**:
```
Devices Connected: ____________
Ports Detected: ______________
Switching Success: ___________
Conflicts: __________________
```

## Test Results Summary

### Device Compatibility Matrix

| Device | Port Detection | Connection | Boot Messages | Commands | High Throughput | Auto-Reconnect | RTS/DTR |
|--------|---------------|------------|---------------|----------|-----------------|----------------|---------|
| ESP32 WROOM | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ |
| ESP32-S2 | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ |
| ESP32-S3 | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ |
| ESP32-C3 | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ |
| NodeMCU | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ |
| Wemos D1 | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ | ⬜ |

Legend: ✅ Pass | ❌ Fail | ⚠️ Partial | ⬜ Not Tested

### Issues Found
```
Issue #1: ________________________________
Severity: ________________________________
Device: __________________________________
Workaround: _____________________________

Issue #2: ________________________________
Severity: ________________________________
Device: __________________________________
Workaround: _____________________________
```

### Recommendations
```
1. _____________________________________
2. _____________________________________
3. _____________________________________
```

## Test Sign-off

**Hardware Testing Complete**: ⬜ Yes ⬜ No  
**All Critical Tests Passed**: ⬜ Yes ⬜ No  
**Ready for Release**: ⬜ Yes ⬜ No  

**Tester Signature**: ________________________  
**Date**: ___________________________________  
**Notes**: __________________________________
