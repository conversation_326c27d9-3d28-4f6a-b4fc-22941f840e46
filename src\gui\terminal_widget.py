"""Terminal widget with syntax highlighting for ESP Serial Monitor Pro."""

import re
from datetime import datetime
from typing import List, Optional
from pathlib import Path

from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import (QTextCharFormat, QColor, QSyntaxHighlighter, 
                          QTextDocument, QFont, QTextCursor)
from PySide6.QtWidgets import (QPlainTextEdit, QWidget, QVBoxLayout, 
                              QHBoxLayout, QLineEdit, QPushButton, 
                              QCheckBox, QLabel, QFileDialog, QMessageBox)

from ..util.logger import logger


class SerialHighlighter(QSyntaxHighlighter):
    """Syntax highlighter for serial terminal output."""
    
    def __init__(self, document: QTextDocument, theme: str = "dark"):
        super().__init__(document)
        self.theme = theme
        self.enabled = True
        self._setup_formats()
    
    def _setup_formats(self):
        """Set up text formats for different line types."""
        self.formats = {}
        
        if self.theme == "dark":
            # Dark theme colors
            self.formats['normal'] = self._create_format(QColor("#00ff88"))  # Green
            self.formats['error'] = self._create_format(QColor("#ff4444"))   # Red
            self.formats['warning'] = self._create_format(QColor("#ffaa00")) # Yellow
            self.formats['timestamp'] = self._create_format(QColor("#888888")) # Gray
        else:
            # Light theme colors
            self.formats['normal'] = self._create_format(QColor("#008844"))  # Dark Green
            self.formats['error'] = self._create_format(QColor("#cc0000"))   # Dark Red
            self.formats['warning'] = self._create_format(QColor("#cc6600")) # Orange
            self.formats['timestamp'] = self._create_format(QColor("#666666")) # Dark Gray
    
    def _create_format(self, color: QColor) -> QTextCharFormat:
        """Create a text format with the given color."""
        format = QTextCharFormat()
        format.setForeground(color)
        return format
    
    def set_theme(self, theme: str):
        """Change the color theme."""
        self.theme = theme
        self._setup_formats()
        self.rehighlight()
    
    def set_enabled(self, enabled: bool):
        """Enable or disable syntax highlighting."""
        self.enabled = enabled
        self.rehighlight()
    
    def highlightBlock(self, text: str):
        """Highlight a block of text."""
        if not self.enabled:
            return
        
        # Check for timestamp at the beginning
        timestamp_match = re.match(r'^\[\d{2}:\d{2}:\d{2}\.\d{3}\]\s*', text)
        if timestamp_match:
            # Highlight timestamp
            self.setFormat(0, timestamp_match.end(), self.formats['timestamp'])
            # Get the rest of the line for classification
            line_content = text[timestamp_match.end():]
            content_start = timestamp_match.end()
        else:
            line_content = text
            content_start = 0
        
        # Classify the line content
        line_type = self._classify_line(line_content)
        
        # Apply formatting to the content part
        if content_start < len(text):
            self.setFormat(content_start, len(text) - content_start, self.formats[line_type])
    
    def _classify_line(self, line: str) -> str:
        """Classify a line based on its content."""
        line_lower = line.lower()
        
        # Error patterns
        error_patterns = [
            r'\berror\b', r'\bfail\b', r'\bexception\b', r'\berr\b', 
            r'\bfatal\b', r'\bcrash\b', r'\babort\b', r'\bpanic\b'
        ]
        
        for pattern in error_patterns:
            if re.search(pattern, line_lower):
                return 'error'
        
        # Warning patterns
        warning_patterns = [
            r'\bwarn\b', r'\bwarning\b', r'\bdebug\b', r'\binfo\b'
        ]
        
        for pattern in warning_patterns:
            if re.search(pattern, line_lower):
                return 'warning'
        
        return 'normal'


class TerminalWidget(QPlainTextEdit):
    """Enhanced terminal widget for serial communication."""
    
    # Signals
    send_requested = Signal(str, str)  # data, line_ending
    
    def __init__(self, parent: Optional[QWidget] = None):
        super().__init__(parent)
        
        # Configuration
        self.max_lines = 10000
        self.auto_scroll_enabled = True
        self.timestamps_enabled = False
        self.line_buffer: List[str] = []
        self.user_scrolled = False
        
        # Set up the widget
        self._setup_ui()
        self._setup_highlighter()
        
        # Update coalescing timer
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._flush_buffer)
        self.update_timer.setSingleShot(True)
        self.pending_lines: List[str] = []
    
    def _setup_ui(self):
        """Set up the terminal UI."""
        # Set font to monospace
        font = QFont("Consolas", 10)
        if not font.exactMatch():
            font = QFont("Courier New", 10)
        self.setFont(font)
        
        # Set read-only
        self.setReadOnly(True)
        
        # Set up scrolling behavior
        self.verticalScrollBar().valueChanged.connect(self._on_scroll)
        
        # Set placeholder text
        self.setPlaceholderText("Serial output will appear here...")
    
    def _setup_highlighter(self, theme: str = "dark"):
        """Set up syntax highlighting."""
        self.highlighter = SerialHighlighter(self.document(), theme)
    
    def _on_scroll(self, value: int):
        """Handle scroll events to detect user scrolling."""
        scrollbar = self.verticalScrollBar()
        # User scrolled if not at the bottom
        self.user_scrolled = value < scrollbar.maximum()
    
    def append_line(self, line: str, force_flush: bool = False):
        """Append a line to the terminal with optional timestamp."""
        if self.timestamps_enabled:
            timestamp = datetime.now().strftime("[%H:%M:%S.%f")[:-3] + "]"
            formatted_line = f"{timestamp} {line}"
        else:
            formatted_line = line
        
        # Add to pending lines for batch processing
        self.pending_lines.append(formatted_line)
        
        # Start or restart the update timer
        if force_flush or len(self.pending_lines) >= 50:  # Flush immediately if many lines
            self._flush_buffer()
        else:
            self.update_timer.start(100)  # 100ms delay for coalescing
    
    def _flush_buffer(self):
        """Flush pending lines to the display."""
        if not self.pending_lines:
            return
        
        # Remember scroll position
        scrollbar = self.verticalScrollBar()
        was_at_bottom = scrollbar.value() == scrollbar.maximum()
        
        # Get current cursor
        cursor = self.textCursor()
        cursor.movePosition(QTextCursor.End)
        
        # Add all pending lines
        for line in self.pending_lines:
            if cursor.position() > 0:
                cursor.insertText("\n")
            cursor.insertText(line)
            
            # Add to line buffer for management
            self.line_buffer.append(line)
        
        # Manage buffer size
        if len(self.line_buffer) > self.max_lines:
            lines_to_remove = len(self.line_buffer) - self.max_lines
            self.line_buffer = self.line_buffer[lines_to_remove:]
            
            # Remove old lines from display
            cursor.movePosition(QTextCursor.Start)
            for _ in range(lines_to_remove):
                cursor.select(QTextCursor.LineUnderCursor)
                cursor.removeSelectedText()
                cursor.deleteChar()  # Remove the newline
        
        # Auto-scroll if enabled and user hasn't scrolled
        if self.auto_scroll_enabled and (was_at_bottom or not self.user_scrolled):
            scrollbar.setValue(scrollbar.maximum())
        
        # Clear pending lines
        self.pending_lines.clear()
    
    def clear_terminal(self):
        """Clear the terminal content."""
        self.clear()
        self.line_buffer.clear()
        self.pending_lines.clear()
        logger.info("Terminal cleared")
    
    def save_log(self, filename: Optional[str] = None) -> bool:
        """Save terminal content to a file."""
        if filename is None:
            filename, _ = QFileDialog.getSaveFileName(
                self,
                "Save Log File",
                f"esp_serial_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "Text Files (*.txt);;All Files (*)"
            )
        
        if not filename:
            return False
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.toPlainText())
            
            logger.info(f"Log saved to {filename}")
            QMessageBox.information(self, "Success", f"Log saved to:\n{filename}")
            return True
            
        except Exception as e:
            error_msg = f"Failed to save log: {e}"
            logger.error(error_msg)
            QMessageBox.critical(self, "Error", error_msg)
            return False
    
    def find_text(self, text: str, case_sensitive: bool = False) -> bool:
        """Find text in the terminal."""
        flags = QTextDocument.FindFlag(0)
        if case_sensitive:
            flags |= QTextDocument.FindCaseSensitively
        
        found = self.find(text, flags)
        if not found:
            # Try from the beginning
            cursor = self.textCursor()
            cursor.movePosition(QTextCursor.Start)
            self.setTextCursor(cursor)
            found = self.find(text, flags)
        
        return found
    
    def set_theme(self, theme: str):
        """Change the color theme."""
        self.highlighter.set_theme(theme)
    
    def set_colorization_enabled(self, enabled: bool):
        """Enable or disable colorization."""
        self.highlighter.set_enabled(enabled)
    
    def set_timestamps_enabled(self, enabled: bool):
        """Enable or disable timestamps."""
        self.timestamps_enabled = enabled
    
    def set_auto_scroll_enabled(self, enabled: bool):
        """Enable or disable auto-scrolling."""
        self.auto_scroll_enabled = enabled
    
    def set_max_lines(self, max_lines: int):
        """Set maximum number of lines to keep."""
        self.max_lines = max_lines
