"""Configuration management for ESP Serial Monitor Pro."""

import json
import os
from typing import Any, Dict, Optional
from pathlib import Path


class Config:
    """Manages application configuration with persistent storage."""
    
    DEFAULT_CONFIG = {
        'last_port': '',
        'last_baudrate': 115200,
        'theme': 'dark',
        'colorization_enabled': True,
        'timestamps_enabled': False,
        'auto_scroll': True,
        'max_lines': 10000,
        'window_geometry': None,
        'line_ending': 'CRLF',
        'auto_reconnect': True,
        'reconnect_interval': 2.0,
        'port_poll_interval': 1.0,
        'send_file_delay': 100,  # milliseconds
        'rts_state': False,
        'dtr_state': False,
    }
    
    def __init__(self):
        """Initialize configuration manager."""
        self.config_dir = Path(os.getenv('APPDATA', '')) / 'ESPSerialMonitorPro'
        self.config_file = self.config_dir / 'config.json'
        self._config = self.DEFAULT_CONFIG.copy()
        self._ensure_config_dir()
        self.load()
    
    def _ensure_config_dir(self) -> None:
        """Ensure configuration directory exists."""
        self.config_dir.mkdir(parents=True, exist_ok=True)
    
    def load(self) -> None:
        """Load configuration from file."""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # Merge with defaults to handle new config keys
                    self._config.update(loaded_config)
            except (json.JSONDecodeError, IOError) as e:
                print(f"Warning: Could not load config: {e}")
                # Use defaults if config is corrupted
    
    def save(self) -> None:
        """Save configuration to file."""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2)
        except IOError as e:
            print(f"Warning: Could not save config: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value."""
        return self._config.get(key, default)
    
    def set(self, key: str, value: Any) -> None:
        """Set configuration value."""
        self._config[key] = value
    
    def reset_to_defaults(self) -> None:
        """Reset configuration to defaults."""
        self._config = self.DEFAULT_CONFIG.copy()
        self.save()
    
    @property
    def last_port(self) -> str:
        return self.get('last_port', '')
    
    @last_port.setter
    def last_port(self, value: str) -> None:
        self.set('last_port', value)
    
    @property
    def last_baudrate(self) -> int:
        return self.get('last_baudrate', 115200)
    
    @last_baudrate.setter
    def last_baudrate(self, value: int) -> None:
        self.set('last_baudrate', value)
    
    @property
    def theme(self) -> str:
        return self.get('theme', 'dark')
    
    @theme.setter
    def theme(self, value: str) -> None:
        self.set('theme', value)
    
    @property
    def colorization_enabled(self) -> bool:
        return self.get('colorization_enabled', True)
    
    @colorization_enabled.setter
    def colorization_enabled(self, value: bool) -> None:
        self.set('colorization_enabled', value)
    
    @property
    def timestamps_enabled(self) -> bool:
        return self.get('timestamps_enabled', False)
    
    @timestamps_enabled.setter
    def timestamps_enabled(self, value: bool) -> None:
        self.set('timestamps_enabled', value)
    
    @property
    def auto_scroll(self) -> bool:
        return self.get('auto_scroll', True)
    
    @auto_scroll.setter
    def auto_scroll(self, value: bool) -> None:
        self.set('auto_scroll', value)
    
    @property
    def max_lines(self) -> int:
        return self.get('max_lines', 10000)
    
    @max_lines.setter
    def max_lines(self, value: int) -> None:
        self.set('max_lines', value)
    
    @property
    def line_ending(self) -> str:
        return self.get('line_ending', 'CRLF')
    
    @line_ending.setter
    def line_ending(self, value: str) -> None:
        self.set('line_ending', value)
    
    @property
    def auto_reconnect(self) -> bool:
        return self.get('auto_reconnect', True)
    
    @auto_reconnect.setter
    def auto_reconnect(self, value: bool) -> None:
        self.set('auto_reconnect', value)
    
    @property
    def reconnect_interval(self) -> float:
        return self.get('reconnect_interval', 2.0)
    
    @reconnect_interval.setter
    def reconnect_interval(self, value: float) -> None:
        self.set('reconnect_interval', value)


# Global configuration instance
config = Config()
