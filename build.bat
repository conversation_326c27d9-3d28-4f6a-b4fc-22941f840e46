@echo off
echo Building ESP Serial Monitor Pro...

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    pause
    exit /b 1
)

REM Check if virtual environment exists, create if not
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip

REM Install requirements
echo Installing requirements...
pip install -r requirements.txt

REM Run tests
echo Running tests...
python -m pytest tests/ -v
if errorlevel 1 (
    echo Tests failed! Please fix issues before building.
    pause
    exit /b 1
)

REM Clean previous build
echo Cleaning previous build...
if exist "dist" rmdir /s /q dist
if exist "build" rmdir /s /q build

REM Build with PyInstaller
echo Building executable...
pyinstaller pyinstaller.spec

REM Check if build was successful
if exist "dist\ESP_Serial_Monitor_Pro.exe" (
    echo.
    echo Build successful!
    echo Executable created: dist\ESP_Serial_Monitor_Pro.exe
    echo.
    echo Running smoke test...
    
    REM Run a quick smoke test
    timeout /t 2 /nobreak >nul
    tasklist | find "ESP_Serial_Monitor_Pro.exe" >nul
    if not errorlevel 1 (
        echo Warning: Application may still be running from previous test
        taskkill /f /im ESP_Serial_Monitor_Pro.exe >nul 2>&1
    )
    
    echo Smoke test completed.
    echo.
    echo Build completed successfully!
    echo You can find the executable in the 'dist' folder.
) else (
    echo Build failed! Check the output above for errors.
    pause
    exit /b 1
)

pause
