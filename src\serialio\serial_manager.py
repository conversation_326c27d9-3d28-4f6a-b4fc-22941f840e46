"""Serial port management for ESP Serial Monitor Pro."""

import time
from typing import List, Optional, NamedTuple
from dataclasses import dataclass

import serial
import serial.tools.list_ports
from PySide6.QtCore import QObject, QThread, Signal, QTimer
from PySide6.QtWidgets import QApplication

from ..util.logger import logger


@dataclass
class PortInfo:
    """Information about a serial port."""
    device: str
    description: str
    hwid: str
    vid: Optional[int] = None
    pid: Optional[int] = None
    
    @property
    def friendly_name(self) -> str:
        """Get a user-friendly port name."""
        if self.description and self.description != "n/a":
            return f"{self.device} - {self.description}"
        return self.device
    
    @classmethod
    def from_list_port_info(cls, port_info) -> 'PortInfo':
        """Create PortInfo from pySerial ListPortInfo."""
        return cls(
            device=port_info.device,
            description=port_info.description or "Unknown Device",
            hwid=port_info.hwid or "",
            vid=port_info.vid,
            pid=port_info.pid
        )


class SerialReaderThread(QThread):
    """Thread for reading serial data without blocking the GUI."""
    
    line_received = Signal(str)
    data_received = Signal(bytes)
    error_occurred = Signal(str)
    connection_lost = Signal()
    
    def __init__(self, serial_port: serial.Serial):
        super().__init__()
        self.serial_port = serial_port
        self.running = False
        self.line_buffer = b""
    
    def run(self):
        """Main thread loop for reading serial data."""
        self.running = True
        logger.info("Serial reader thread started")
        
        while self.running and self.serial_port and self.serial_port.is_open:
            try:
                # Check for available data
                if self.serial_port.in_waiting > 0:
                    # Read available data
                    data = self.serial_port.read(self.serial_port.in_waiting)
                    if data:
                        self.data_received.emit(data)
                        self._process_data(data)
                else:
                    # Small sleep to prevent busy waiting
                    self.msleep(10)
                    
            except serial.SerialException as e:
                logger.error(f"Serial read error: {e}")
                self.error_occurred.emit(str(e))
                self.connection_lost.emit()
                break
            except Exception as e:
                logger.error(f"Unexpected error in serial reader: {e}")
                self.error_occurred.emit(f"Unexpected error: {e}")
                break
        
        logger.info("Serial reader thread stopped")
    
    def _process_data(self, data: bytes):
        """Process incoming data and emit complete lines."""
        self.line_buffer += data
        
        # Split on line endings and process complete lines
        while b'\n' in self.line_buffer:
            line, self.line_buffer = self.line_buffer.split(b'\n', 1)
            
            # Remove carriage return if present
            if line.endswith(b'\r'):
                line = line[:-1]
            
            # Decode to string with error handling
            try:
                line_str = line.decode('utf-8', errors='replace')
            except UnicodeDecodeError:
                line_str = line.decode('latin-1', errors='replace')
            
            if line_str:  # Only emit non-empty lines
                self.line_received.emit(line_str)
    
    def stop(self):
        """Stop the reader thread."""
        self.running = False
        self.wait(3000)  # Wait up to 3 seconds for thread to finish


class PortWatcherThread(QThread):
    """Thread for monitoring serial port changes."""
    
    ports_changed = Signal(list)
    
    def __init__(self, poll_interval: float = 1.0):
        super().__init__()
        self.poll_interval = poll_interval
        self.running = False
        self.last_ports = []
    
    def run(self):
        """Main thread loop for monitoring ports."""
        self.running = True
        logger.info("Port watcher thread started")
        
        while self.running:
            try:
                current_ports = self._get_current_ports()
                
                # Check if ports have changed
                if current_ports != self.last_ports:
                    logger.info(f"Port list changed: {len(current_ports)} ports available")
                    self.ports_changed.emit(current_ports)
                    self.last_ports = current_ports
                
                # Sleep for poll interval
                self.msleep(int(self.poll_interval * 1000))
                
            except Exception as e:
                logger.error(f"Error in port watcher: {e}")
                self.msleep(5000)  # Wait 5 seconds before retrying
        
        logger.info("Port watcher thread stopped")
    
    def _get_current_ports(self) -> List[PortInfo]:
        """Get current list of available ports."""
        try:
            ports = serial.tools.list_ports.comports()
            return [PortInfo.from_list_port_info(port) for port in ports]
        except Exception as e:
            logger.error(f"Error listing ports: {e}")
            return []
    
    def stop(self):
        """Stop the port watcher thread."""
        self.running = False
        self.wait(3000)


class SerialManager(QObject):
    """Manages serial port connections and communication."""
    
    # Signals
    connected = Signal(str)  # port name
    disconnected = Signal()
    connection_failed = Signal(str)  # error message
    line_received = Signal(str)
    data_received = Signal(bytes)
    ports_changed = Signal(list)
    
    # Common baud rates for ESP devices
    COMMON_BAUD_RATES = [9600, 74880, 115200, 230400, 460800, 921600]
    
    def __init__(self):
        super().__init__()
        self.serial_port: Optional[serial.Serial] = None
        self.reader_thread: Optional[SerialReaderThread] = None
        self.port_watcher: Optional[PortWatcherThread] = None
        self.current_port = ""
        self.current_baudrate = 115200
        
        # Auto-reconnect timer
        self.reconnect_timer = QTimer()
        self.reconnect_timer.timeout.connect(self._attempt_reconnect)
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10
        
        # Start port monitoring
        self.start_port_monitoring()
    
    def start_port_monitoring(self, poll_interval: float = 1.0):
        """Start monitoring for port changes."""
        if self.port_watcher is None or not self.port_watcher.isRunning():
            self.port_watcher = PortWatcherThread(poll_interval)
            self.port_watcher.ports_changed.connect(self.ports_changed.emit)
            self.port_watcher.start()
    
    def stop_port_monitoring(self):
        """Stop monitoring for port changes."""
        if self.port_watcher:
            self.port_watcher.stop()
            self.port_watcher = None
    
    def get_available_ports(self) -> List[PortInfo]:
        """Get list of currently available serial ports."""
        try:
            ports = serial.tools.list_ports.comports()
            return [PortInfo.from_list_port_info(port) for port in ports]
        except Exception as e:
            logger.error(f"Error getting available ports: {e}")
            return []
    
    def connect(self, port: str, baudrate: int = 115200, timeout: float = 1.0) -> bool:
        """Connect to a serial port."""
        if self.is_connected():
            self.disconnect()
        
        try:
            logger.info(f"Attempting to connect to {port} at {baudrate} baud")
            
            self.serial_port = serial.Serial(
                port=port,
                baudrate=baudrate,
                timeout=timeout,
                write_timeout=timeout
            )
            
            if self.serial_port.is_open:
                self.current_port = port
                self.current_baudrate = baudrate
                
                # Start reader thread
                self.reader_thread = SerialReaderThread(self.serial_port)
                self.reader_thread.line_received.connect(self.line_received.emit)
                self.reader_thread.data_received.connect(self.data_received.emit)
                self.reader_thread.connection_lost.connect(self._handle_connection_lost)
                self.reader_thread.start()
                
                # Reset reconnect attempts
                self.reconnect_attempts = 0
                self.reconnect_timer.stop()
                
                logger.info(f"Successfully connected to {port}")
                self.connected.emit(port)
                return True
            
        except serial.SerialException as e:
            error_msg = f"Failed to connect to {port}: {e}"
            logger.error(error_msg)
            self.connection_failed.emit(error_msg)
            
        except Exception as e:
            error_msg = f"Unexpected error connecting to {port}: {e}"
            logger.error(error_msg)
            self.connection_failed.emit(error_msg)
        
        return False

    def disconnect(self):
        """Disconnect from the current serial port."""
        logger.info("Disconnecting from serial port")

        # Stop reconnect timer
        self.reconnect_timer.stop()

        # Stop reader thread
        if self.reader_thread:
            self.reader_thread.stop()
            self.reader_thread = None

        # Close serial port
        if self.serial_port and self.serial_port.is_open:
            try:
                self.serial_port.close()
            except Exception as e:
                logger.error(f"Error closing serial port: {e}")

        self.serial_port = None
        self.current_port = ""
        self.disconnected.emit()

    def is_connected(self) -> bool:
        """Check if currently connected to a serial port."""
        return (self.serial_port is not None and
                self.serial_port.is_open and
                self.reader_thread is not None and
                self.reader_thread.isRunning())

    def send_data(self, data: str, line_ending: str = "CRLF") -> bool:
        """Send data to the serial port."""
        if not self.is_connected():
            logger.warning("Attempted to send data while not connected")
            return False

        try:
            # Add line ending
            if line_ending == "CR":
                data += "\r"
            elif line_ending == "LF":
                data += "\n"
            elif line_ending == "CRLF":
                data += "\r\n"
            # "None" means no line ending

            # Encode and send
            encoded_data = data.encode('utf-8')
            bytes_written = self.serial_port.write(encoded_data)
            self.serial_port.flush()

            logger.debug(f"Sent {bytes_written} bytes: {repr(data)}")
            return True

        except serial.SerialException as e:
            logger.error(f"Error sending data: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending data: {e}")
            return False

    def set_rts_dtr(self, rts: bool, dtr: bool):
        """Set RTS and DTR line states."""
        if not self.is_connected():
            return

        try:
            self.serial_port.setRTS(rts)
            self.serial_port.setDTR(dtr)
            logger.debug(f"Set RTS={rts}, DTR={dtr}")
        except Exception as e:
            logger.error(f"Error setting RTS/DTR: {e}")

    def _handle_connection_lost(self):
        """Handle when connection is lost."""
        logger.warning("Serial connection lost")

        # Clean up current connection
        if self.reader_thread:
            self.reader_thread.stop()
            self.reader_thread = None

        if self.serial_port:
            try:
                self.serial_port.close()
            except:
                pass
            self.serial_port = None

        self.disconnected.emit()

        # Start auto-reconnect if enabled
        if self.current_port and self.reconnect_attempts < self.max_reconnect_attempts:
            self.reconnect_attempts += 1
            reconnect_delay = min(1000 * (2 ** (self.reconnect_attempts - 1)), 10000)  # Exponential backoff, max 10s
            logger.info(f"Will attempt reconnect #{self.reconnect_attempts} in {reconnect_delay}ms")
            self.reconnect_timer.start(reconnect_delay)

    def _attempt_reconnect(self):
        """Attempt to reconnect to the last port."""
        self.reconnect_timer.stop()

        if self.current_port:
            logger.info(f"Attempting reconnect to {self.current_port} (attempt {self.reconnect_attempts})")
            success = self.connect(self.current_port, self.current_baudrate)

            if not success and self.reconnect_attempts < self.max_reconnect_attempts:
                # Schedule next attempt
                self.reconnect_attempts += 1
                reconnect_delay = min(1000 * (2 ** (self.reconnect_attempts - 1)), 10000)
                self.reconnect_timer.start(reconnect_delay)
            elif not success:
                logger.warning(f"Max reconnect attempts ({self.max_reconnect_attempts}) reached")

    def cleanup(self):
        """Clean up resources."""
        self.disconnect()
        self.stop_port_monitoring()
